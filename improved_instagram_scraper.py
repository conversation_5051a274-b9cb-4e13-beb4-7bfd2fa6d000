#!/usr/bin/env python3
"""
Improved Instagram Scraper with Enhanced Bot Detection Evasion
"""

import asyncio
import sys
import tempfile
import time
import random
from playwright.async_api import async_playwright

# Add the app directory to the Python path
sys.path.append('/home/<USER>/WebReview_DS_API_24Jun/app')

class ImprovedInstagramScraper:
    def __init__(self):
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15"
        ]
        
    async def setup_stealth_browser(self, playwright):
        """Setup a browser with stealth configurations"""
        browser = await playwright.chromium.launch(
            headless=True,
            args=[
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-blink-features=AutomationControlled',
                '--disable-extensions',
                '--no-first-run',
                '--disable-default-apps',
                '--disable-sync',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-field-trial-config',
                '--disable-back-forward-cache',
                '--disable-ipc-flooding-protection',
                '--enable-features=NetworkService,NetworkServiceLogging',
                '--force-color-profile=srgb',
                '--metrics-recording-only',
                '--use-mock-keychain',
                '--disable-component-extensions-with-background-pages',
                '--disable-default-apps',
                '--mute-audio',
                '--no-default-browser-check',
                '--autoplay-policy=user-gesture-required',
                '--disable-background-networking',
                '--disable-background-timer-throttling',
                '--disable-client-side-phishing-detection',
                '--disable-component-update',
                '--disable-default-apps',
                '--disable-domain-reliability',
                '--disable-extensions',
                '--disable-features=TranslateUI',
                '--disable-hang-monitor',
                '--disable-ipc-flooding-protection',
                '--disable-popup-blocking',
                '--disable-prompt-on-repost',
                '--disable-renderer-backgrounding',
                '--disable-sync',
                '--force-color-profile=srgb',
                '--metrics-recording-only',
                '--no-first-run',
                '--enable-automation',
                '--password-store=basic',
                '--use-mock-keychain'
            ]
        )
        
        context = await browser.new_context(
            user_agent=random.choice(self.user_agents),
            viewport={'width': 1920, 'height': 1080},
            locale='en-US',
            timezone_id='America/New_York',
            permissions=['geolocation'],
            geolocation={'latitude': 40.7128, 'longitude': -74.0060},  # New York
            extra_http_headers={
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
        )
        
        page = await context.new_page()
        
        # Add stealth scripts
        await page.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });
            
            window.chrome = {
                runtime: {},
            };
            
            Object.defineProperty(navigator, 'permissions', {
                get: () => ({
                    query: () => Promise.resolve({ state: 'granted' }),
                }),
            });
        """)
        
        return browser, page
    
    async def try_instagram_without_login(self, url: str):
        """Try to access Instagram without login using various techniques"""
        print(f"🔍 Trying Instagram without login: {url}")
        
        async with async_playwright() as p:
            browser, page = await self.setup_stealth_browser(p)
            
            try:
                # Method 1: Try direct access
                print("Method 1: Direct access")
                response = await page.goto(url, timeout=30000, wait_until='domcontentloaded')
                print(f"Response status: {response.status}")
                
                await page.wait_for_timeout(3000)
                current_url = page.url
                print(f"Current URL: {current_url}")
                
                if "/accounts/login" not in current_url:
                    print("✅ Success: No login redirect")
                    return await self.capture_and_analyze(page, url, "direct_access")
                
                # Method 2: Try with embed parameter
                print("Method 2: Trying with embed parameter")
                embed_url = f"{url}?__a=1&__d=dis"
                await page.goto(embed_url, timeout=30000, wait_until='domcontentloaded')
                await page.wait_for_timeout(3000)
                
                current_url = page.url
                if "/accounts/login" not in current_url:
                    print("✅ Success: Embed parameter worked")
                    return await self.capture_and_analyze(page, embed_url, "embed_param")
                
                # Method 3: Try mobile version
                print("Method 3: Trying mobile version")
                await page.set_user_agent("Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1")
                await page.set_viewport_size({"width": 375, "height": 812})
                
                mobile_url = url.replace("www.instagram.com", "m.instagram.com")
                await page.goto(mobile_url, timeout=30000, wait_until='domcontentloaded')
                await page.wait_for_timeout(3000)
                
                current_url = page.url
                if "/accounts/login" not in current_url:
                    print("✅ Success: Mobile version worked")
                    return await self.capture_and_analyze(page, mobile_url, "mobile_version")
                
                # Method 4: Try with different referrer
                print("Method 4: Trying with Google referrer")
                await page.set_extra_http_headers({
                    'Referer': 'https://www.google.com/',
                    'Sec-Fetch-Site': 'cross-site',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-User': '?1',
                    'Sec-Fetch-Dest': 'document'
                })
                
                await page.goto(url, timeout=30000, wait_until='domcontentloaded')
                await page.wait_for_timeout(3000)
                
                current_url = page.url
                if "/accounts/login" not in current_url:
                    print("✅ Success: Google referrer worked")
                    return await self.capture_and_analyze(page, url, "google_referrer")
                
                print("❌ All methods failed - login required")
                return await self.capture_and_analyze(page, url, "login_required")
                
            except Exception as e:
                print(f"❌ Error: {str(e)}")
                return None
            finally:
                await browser.close()
    
    async def capture_and_analyze(self, page, url: str, method: str):
        """Capture screenshot and analyze the page"""
        print(f"📸 Capturing screenshot using method: {method}")
        
        # Wait for content to load
        await page.wait_for_timeout(2000)
        
        # Get page info
        title = await page.title()
        current_url = page.url
        
        # Get page content
        try:
            body_text = await page.evaluate("() => document.body.innerText")
            content_length = len(body_text.strip())
        except:
            content_length = 0
        
        print(f"Page title: {title}")
        print(f"Content length: {content_length} characters")
        print(f"Final URL: {current_url}")
        
        # Check if page has meaningful content
        is_blank = content_length < 100
        is_login_page = "/accounts/login" in current_url or "login" in title.lower()
        
        if is_blank:
            print("⚠️  Page appears to be blank or has minimal content")
        elif is_login_page:
            print("🔐 Page is showing login form")
        else:
            print("✅ Page has content")
        
        # Take screenshot
        screenshot_bytes = await page.screenshot(full_page=True, type="png")
        
        # Save screenshot
        with tempfile.NamedTemporaryFile(delete=False, suffix=".png", prefix=f"instagram_{method}_") as temp_file:
            temp_file.write(screenshot_bytes)
            temp_path = temp_file.name
        
        print(f"📁 Screenshot saved: {temp_path} ({len(screenshot_bytes)} bytes)")
        
        return {
            'method': method,
            'url': url,
            'final_url': current_url,
            'title': title,
            'content_length': content_length,
            'is_blank': is_blank,
            'is_login_page': is_login_page,
            'screenshot_path': temp_path,
            'screenshot_size': len(screenshot_bytes)
        }

async def main():
    """Main test function"""
    print("🚀 Improved Instagram Scraping Test")
    print("=" * 60)
    
    scraper = ImprovedInstagramScraper()
    
    test_urls = [
        "https://www.instagram.com/",
        "https://www.instagram.com/nike",
        "https://www.instagram.com/supertails.official",
        "https://www.instagram.com/natgeo"
    ]
    
    results = []
    
    for url in test_urls:
        print(f"\n{'='*60}")
        print(f"Testing URL: {url}")
        print(f"{'='*60}")
        
        result = await scraper.try_instagram_without_login(url)
        if result:
            results.append(result)
        
        # Small delay between tests
        await asyncio.sleep(2)
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 SUMMARY")
    print(f"{'='*60}")
    
    for result in results:
        print(f"URL: {result['url']}")
        print(f"Method: {result['method']}")
        print(f"Success: {'✅' if not result['is_blank'] and not result['is_login_page'] else '❌'}")
        print(f"Content: {result['content_length']} chars")
        print(f"Screenshot: {result['screenshot_path']}")
        print("-" * 40)

if __name__ == "__main__":
    asyncio.run(main())
