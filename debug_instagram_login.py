#!/usr/bin/env python3
"""
Debug Instagram Login Page Structure
"""

import asyncio
import sys
import tempfile
from playwright.async_api import async_playwright

async def debug_instagram_login():
    """Debug Instagram login page to understand current structure"""
    print("🔍 Debugging Instagram Login Page Structure")
    print("=" * 60)
    
    async with async_playwright() as p:
        # Try different browsers
        browsers = [
            ("Chromium", p.chromium),
            ("Firefox", p.firefox),
        ]
        
        for browser_name, browser_type in browsers:
            print(f"\n--- Testing with {browser_name} ---")
            
            try:
                browser = await browser_type.launch(
                    headless=False,  # Run in visible mode to see what's happening
                    args=[
                        '--no-sandbox',
                        '--disable-setuid-sandbox',
                        '--disable-dev-shm-usage',
                        '--disable-web-security',
                        '--disable-features=VizDisplayCompositor',
                        '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                    ]
                )
                
                page = await browser.new_page()
                
                # Set viewport
                await page.set_viewport_size({"width": 1920, "height": 1080})
                
                # Navigate to Instagram
                url = "https://www.instagram.com/"
                print(f"Navigating to: {url}")
                
                response = await page.goto(url, timeout=30000)
                print(f"Response status: {response.status}")
                
                # Wait for page to load
                await page.wait_for_timeout(5000)
                
                # Check current URL
                current_url = page.url
                print(f"Current URL: {current_url}")
                
                # Check page title
                title = await page.title()
                print(f"Page title: {title}")
                
                # Check if we're on login page
                if "/accounts/login" in current_url:
                    print("🔐 On login page - analyzing structure...")
                    
                    # Get page HTML
                    html_content = await page.content()
                    print(f"HTML content length: {len(html_content)} characters")
                    
                    # Look for various input field selectors
                    selectors_to_try = [
                        'input[name="username"]',
                        'input[type="text"]',
                        'input[placeholder*="username"]',
                        'input[placeholder*="Username"]',
                        'input[placeholder*="email"]',
                        'input[placeholder*="Email"]',
                        'input[aria-label*="username"]',
                        'input[aria-label*="Username"]',
                        'input[data-testid*="username"]',
                        'input[autocomplete="username"]',
                        'input[id*="username"]',
                        'input[class*="username"]',
                        'input[class*="login"]',
                        'input[class*="form"]',
                        'input',  # All inputs
                    ]
                    
                    print("\n🔍 Searching for input fields...")
                    for selector in selectors_to_try:
                        try:
                            elements = await page.query_selector_all(selector)
                            if elements:
                                print(f"✅ Found {len(elements)} element(s) with selector: {selector}")
                                
                                # Get details about first element
                                if elements:
                                    element = elements[0]
                                    attrs = await element.evaluate('''
                                        (el) => {
                                            const attrs = {};
                                            for (let attr of el.attributes) {
                                                attrs[attr.name] = attr.value;
                                            }
                                            return attrs;
                                        }
                                    ''')
                                    print(f"   First element attributes: {attrs}")
                            else:
                                print(f"❌ No elements found with selector: {selector}")
                        except Exception as e:
                            print(f"❌ Error with selector {selector}: {str(e)}")
                    
                    # Look for forms
                    print("\n🔍 Searching for forms...")
                    forms = await page.query_selector_all('form')
                    print(f"Found {len(forms)} form(s)")
                    
                    for i, form in enumerate(forms):
                        form_attrs = await form.evaluate('''
                            (el) => {
                                const attrs = {};
                                for (let attr of el.attributes) {
                                    attrs[attr.name] = attr.value;
                                }
                                return attrs;
                            }
                        ''')
                        print(f"Form {i+1} attributes: {form_attrs}")
                        
                        # Get inputs within this form
                        form_inputs = await form.query_selector_all('input')
                        print(f"  Form {i+1} has {len(form_inputs)} input(s)")
                        
                        for j, inp in enumerate(form_inputs):
                            inp_attrs = await inp.evaluate('''
                                (el) => {
                                    const attrs = {};
                                    for (let attr of el.attributes) {
                                        attrs[attr.name] = attr.value;
                                    }
                                    return attrs;
                                }
                            ''')
                            print(f"    Input {j+1}: {inp_attrs}")
                    
                    # Take screenshot
                    screenshot_bytes = await page.screenshot(full_page=True, type="png")
                    
                    with tempfile.NamedTemporaryFile(delete=False, suffix=".png", prefix=f"instagram_debug_{browser_name.lower()}_") as temp_file:
                        temp_file.write(screenshot_bytes)
                        temp_path = temp_file.name
                    
                    print(f"📁 Screenshot saved: {temp_path}")
                    
                    # Wait a bit to see the page
                    print("⏳ Waiting 10 seconds for manual inspection...")
                    await page.wait_for_timeout(10000)
                
                else:
                    print("✅ Not redirected to login page")
                    
                    # Take screenshot of main page
                    screenshot_bytes = await page.screenshot(full_page=True, type="png")
                    
                    with tempfile.NamedTemporaryFile(delete=False, suffix=".png", prefix=f"instagram_main_{browser_name.lower()}_") as temp_file:
                        temp_file.write(screenshot_bytes)
                        temp_path = temp_file.name
                    
                    print(f"📁 Screenshot saved: {temp_path}")
                
                await browser.close()
                
            except Exception as e:
                print(f"❌ Error with {browser_name}: {str(e)}")
                if 'browser' in locals():
                    await browser.close()

if __name__ == "__main__":
    asyncio.run(debug_instagram_login())
