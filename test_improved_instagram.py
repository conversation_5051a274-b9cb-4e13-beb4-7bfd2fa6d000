#!/usr/bin/env python3
"""
Test the improved Instagram scraping functionality
"""

import asyncio
import sys
import tempfile
import time
from playwright.async_api import async_playwright

# Add the app directory to the Python path
sys.path.append('/home/<USER>/WebReview_DS_API_24Jun/app')

from services.screenshot.playwright_driver import capture_screenshot

async def test_instagram_improvements():
    """Test the improved Instagram scraping"""
    print("🔍 Testing Improved Instagram Scraping")
    print("=" * 60)
    
    test_urls = [
        "https://www.instagram.com/",
        "https://www.instagram.com/nike",
        "https://www.instagram.com/supertails.official"
    ]
    
    async with async_playwright() as p:
        for i, url in enumerate(test_urls):
            print(f"\n--- Test {i+1}: {url} ---")
            start_time = time.time()
            
            try:
                screenshot_bytes = await capture_screenshot(
                    playwright=p,
                    url=url,
                    close_popups=True,
                    image_load_wait=3000
                )
                
                end_time = time.time()
                duration = end_time - start_time
                
                if screenshot_bytes:
                    # Save screenshot
                    with tempfile.NamedTemporaryFile(delete=False, suffix=".png", prefix=f"improved_instagram_{i+1}_") as temp_file:
                        temp_file.write(screenshot_bytes)
                        temp_path = temp_file.name
                    
                    print(f"✅ SUCCESS: Screenshot captured")
                    print(f"📁 File: {temp_path}")
                    print(f"📏 Size: {len(screenshot_bytes)} bytes")
                    print(f"⏱️  Duration: {duration:.2f} seconds")
                    
                    # Check if screenshot is likely blank
                    if len(screenshot_bytes) < 10000:  # Less than 10KB is likely blank
                        print(f"⚠️  WARNING: Screenshot is small - might be blank")
                    else:
                        print(f"✅ Screenshot appears to have content")
                        
                else:
                    print(f"❌ FAILED: No screenshot captured")
                    print(f"⏱️  Duration: {duration:.2f} seconds")
                    
            except Exception as e:
                end_time = time.time()
                duration = end_time - start_time
                print(f"❌ ERROR: {str(e)}")
                print(f"⏱️  Duration: {duration:.2f} seconds")
            
            # Small delay between tests
            await asyncio.sleep(3)
    
    print(f"\n{'='*60}")
    print("🏁 Testing Complete")
    print(f"{'='*60}")

if __name__ == "__main__":
    asyncio.run(test_instagram_improvements())
